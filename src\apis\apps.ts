import { createApi } from '@vtj/utils';
import { REMOTE, type ApiListResponse, type LowCodeAppVO } from '@/shared';

export interface SaveLowCodeAppReq {
  name: string;
  label: string;
  platform: string;
  scope?: string;
  userId?: string;
  id?: string; // 添加id字段用于编辑模式
}

export interface FindLowCodeAppsReq {
  page: number;
  limit: number;
}

export type FindLowCodeAppsRes = {
  id: string;
} & SaveLowCodeAppReq;

/**
 * 新建、更新应用
 */
export const saveLowCodeApp = (data: SaveLowCodeAppReq) => {
  console.log('调用保存应用API，数据:', data);

  // 判断是创建还是更新
  const isUpdate = !!data.id;
  const url = isUpdate ? `/apps/${data.name}` : '/apps';
  const method = isUpdate ? 'put' : 'post';

  console.log(`${isUpdate ? '更新' : '创建'}应用API调用:`, { url, method, data });

  const api = createApi<LowCodeAppVO>({
    baseURL: REMOTE,
    url,
    method,
    settings: {
      type: 'json'
    }
  });

  return api(data)
    .then((res) => {
      console.log(`${isUpdate ? '更新' : '创建'}应用API成功响应:`, res);
      return res;
    })
    .catch((error) => {
      console.error(`${isUpdate ? '更新' : '创建'}应用API错误:`, error);
      throw error;
    });
};

/**
 * 获取我的创建的应用
 */
export const findLowCodeApps = createApi<
  ApiListResponse<FindLowCodeAppsRes>,
  FindLowCodeAppsReq
>({
  baseURL: REMOTE,
  url: '/apps/action/find-my-apps'
});

/**
 * 删除我的应用
 * @param id
 * @returns
 */
export const removeLowCodeApp = (id: string) => {
  const api = createApi({
    baseURL: REMOTE,
    url: '/apps/:id',
    method: 'delete'
  });
  return api(null, { params: { id } })
    .then((res) => {
      console.log('删除API成功响应:', res);
      return res;
    })
    .catch((error) => {
      console.log('删除API catch块:', error);
      // 检查是否是createApi的特殊错误格式但实际成功
      if (error?.code === 200 && error?.message === 'success') {
        console.log('删除成功（从错误对象中提取）');
        return error;
      }
      // 如果是真正的错误，重新抛出
      console.error('删除API真正的错误:', error);
      throw error;
    });
};

/**
 * 获取应用详情
 * @param name
 * @returns
 */
export const getLowCodeApp = (name: string) => {
  const api = createApi<LowCodeAppVO>({
    baseURL: REMOTE,
    url: '/apps/:name',
    method: 'get'
  });
  return api(null, { params: { name } });
};
