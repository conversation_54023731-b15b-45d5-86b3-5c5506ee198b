<template>
  <ElCard class="app-card" shadow="hover" @click="onDesign">
    <div class="title">{{ props.item.label }}</div>
    <div class="name">{{ props.item.name }}</div>
    <ElTag class="platform" :type="tagType">{{ props.item.platform }}</ElTag>
    <template #footer>
      <div class="action">
        <ElButton size="small" :icon="View" plain @click.stop="onPreview">
          浏览
        </ElButton>
        <ElButton size="small" :icon="Setting" plain @click.stop="onDesign">
          设计
        </ElButton>
        <ElButton size="small" :icon="Edit" plain @click.stop="onEdit"
          >编辑</ElButton
        >
        <ElButton
          size="small"
          :icon="Delete"
          type="danger"
          plain
          @click.stop="onRemove">
          删除
        </ElButton>
      </div>
    </template>
  </ElCard>
</template>
<script lang="ts" setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElCard, ElButton, ElTag, ElMessageBox, ElNotification } from 'element-plus';
import { Setting, Edit, View, Delete } from '@element-plus/icons-vue';
import type { PlatformType } from '@vtj/core';
import { type LowCodeAppVO } from '@/shared';
import { createPreviewPath } from '@/utils';
import { removeLowCodeApp } from '@/apis';

export interface Props {
  item: LowCodeAppVO;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  refresh: [];
  edit: [item: LowCodeAppVO];
}>();

const router = useRouter();

const tagType = computed(() => {
  const map = {
    Web: 'primary',
    H5: 'warning',
    UniApp: 'danger'
  };
  return map[props.item.platform] as any;
});

const onRemove = async () => {
  if (props.item.id) {
    try {
      const ret = await ElMessageBox.confirm('确认删除该应用？', {
        type: 'warning',
        title: '提示',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      });

      if (ret) {
        console.log('开始删除应用，ID:', props.item.id);

        try {
          const res = await removeLowCodeApp(props.item.id);
          console.log('删除应用响应:', res);

          // 检查删除是否成功（检查响应中的成功标识）
          if (res && (res.code === 200 || res.message === 'success' || res.data === true)) {
            // ElNotification({
            //   title: '成功',
            //   message: '应用删除成功',
            //   type: 'success'
            // }); // 注释掉删除成功提示
            emit('refresh');
          } else {
            ElNotification({
              title: '错误',
              message: '删除应用失败',
              type: 'error'
            });
          }
        } catch (deleteError) {
          console.error('删除应用API调用失败:', deleteError);
          ElNotification({
            title: '错误',
            message: '删除应用时发生错误',
            type: 'error'
          });
        }
      }
    } catch (confirmError) {
      // 用户取消删除，不需要显示错误
      if (confirmError !== 'cancel') {
        console.error('删除确认对话框错误:', confirmError);
      }
    }
  }
};

const onEdit = () => {
  emit('edit', props.item);
};

const onDesign = () => {
  const { platform, name } = props.item;
  router.push(`/${platform.toLowerCase()}/${name}`);
};

const onPreview = () => {
  const { platform, name } = props.item;
  const url = createPreviewPath(name, platform.toLowerCase() as PlatformType);
  window.open(url);
};
</script>

<style lang="scss" scoped>
.app-card {
  margin-bottom: 20px;
  position: relative;

  :deep(.el-card__body) {
    background-color: var(--el-fill-color);
    display: flex;
    justify-content: center;
    align-items: center;
    height: 250px;
    flex-direction: column;
    cursor: pointer;
  }
  .title {
    font-size: 24px;
    margin-bottom: 5px;
  }

  .name {
    color: var(--el-text-color-secondary);
  }
  .platform {
    position: absolute;
    right: 20px;
    top: 20px;
  }
}
.action {
  display: flex;
  justify-content: space-around;
}
</style>
