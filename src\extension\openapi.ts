import {
  OpenApi,
  Access,
  type PlatformType,
  type TemplateDto,
  type BlockSchema,
  type DictOption,
  type PublishTemplateDto,
  type TopicDto,
  type ResponseWrapper,
  type AITopic,
  type AIChat,
  type ChatDto,
  type Settings,
  type CompletionChunk
} from '@vtj/pro';
import {
  loginBySign,
  getLoginUser,
  getTemplates,
  getTemplateById,
  removeTemplate,
  getTemplateDsl,
  getDictOptions,
  publishTemplate,
  postTopic,
  postImageTopic,
  postJsonTopic,
  getChats,
  getTopics,
  postChat,
  saveChat,
  cancelChat,
  removeTopic,
  getHotTopics,
  chatCompletions,
  getSettins,
  createOrder,
  cancelOrder,
  getOrder
} from '@/apis';
import { AUTH_CODE, REMOTE } from '@/shared';

export class CustomOpenApi implements OpenApi {
  constructor(private access: Access) {}
  /**
   * 用签名自动登录
   * @returns
   */
  async loginBySign(): Promise<string[]> {
    const data = await loginBySign(AUTH_CODE || '').catch(() => null);
    return data as string[];
  }

  /**
   * 验证用户登录是否有效
   * @param token
   * @returns
   */
  async isLogined(): Promise<boolean> {
    const token = this.access.getToken();
    if (token) {
      return !!(await getLoginUser(token).catch(() => null));
    }
    return false;
  }

  async getTemplates(platform: PlatformType): Promise<TemplateDto[]> {
    const token = this.access.getToken();
    return await getTemplates(platform, token).catch(() => []);
  }

  async getTemplateById(id: string): Promise<TemplateDto> {
    const token = this.access.getToken();
    return await getTemplateById(id, token).catch(() => null);
  }

  async removeTemplate(id: string): Promise<boolean> {
    const token = this.access.getToken();
    const res = await removeTemplate(id, token).catch(() => false);
    return !!res;
  }

  async getTemplateDsl(id: string): Promise<BlockSchema> {
    const token = this.access.getToken();
    return await getTemplateDsl(id, token).catch(() => null);
  }

  async getDictOptions(code: string): Promise<DictOption[]> {
    return await getDictOptions(code).catch(() => []);
  }

  async publishTemplate(dto: PublishTemplateDto): Promise<boolean> {
    const token = this.access.getToken();
    return await publishTemplate(dto, token).catch(() => false);
  }

  async postTopic(
    dto: TopicDto
  ): Promise<ResponseWrapper<{ topic: AITopic; chat: AIChat }>> {
    const token = this.access.getToken();
    return await postTopic(dto, token).then((res) => res.data);
  }

  async postImageTopic(
    dto: TopicDto
  ): Promise<ResponseWrapper<{ topic: AITopic; chat: AIChat }>> {
    const token = this.access.getToken();
    return await postImageTopic(dto, token).then((res) => res.data);
  }

  async postJsonTopic(
    dto: TopicDto
  ): Promise<ResponseWrapper<{ topic: AITopic; chat: AIChat }>> {
    const token = this.access.getToken();
    return await postJsonTopic(dto, token).then((res) => res.data);
  }

  async getChats(topicId: string): Promise<ResponseWrapper<AIChat[]>> {
    const token = this.access.getToken();
    return await getChats(topicId, token).then((res) => res.data);
  }

  async getTopics(fileId: string): Promise<ResponseWrapper<AITopic[]>> {
    const token = this.access.getToken();
    return await getTopics(fileId, token).then((res) => res.data);
  }

  async postChat(dto: ChatDto): Promise<ResponseWrapper<AIChat>> {
    const token = this.access.getToken();
    return await postChat(dto, token).then((res) => res.data);
  }

  async saveChat(chat: AIChat): Promise<ResponseWrapper<boolean>> {
    const token = this.access.getToken();
    return await saveChat(chat, token).then((res) => res.data);
  }

  async cancelChat(chat: AIChat): Promise<ResponseWrapper<AIChat>> {
    const token = this.access.getToken();
    return await cancelChat(chat.id, token).then((res) => res.data);
  }

  async removeTopic(topicId: string): Promise<ResponseWrapper<boolean>> {
    const token = this.access.getToken();
    return await removeTopic(topicId, token).then((res) => res.data);
  }

  async getHotTopics(): Promise<ResponseWrapper<AITopic[]>> {
    return await getHotTopics()
      .then((res) => res.data)
      .catch(() => []);
  }

  async chatCompletions(
    topicId: string,
    chatId: string,
    callback?: (data: CompletionChunk, done?: boolean) => void,
    error?: (err: Error, cancel?: boolean) => void
  ): Promise<() => void> {
    const token = this.access.getToken();
    const { abort, promise } = chatCompletions(topicId, chatId, token);

    promise
      .then(async (res) => {
        const reader = res.body?.getReader();
        if (!reader) return;
        const decoder = new TextDecoder();
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            callback && callback(null as any, true);
            break;
          }

          const lines = decoder.decode(value).split('\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data: CompletionChunk = JSON.parse(line.slice(6));
                callback && callback(data, done);
              } catch (e: any) {
                const msg = line.slice(6);
                error && error(msg ? new Error(msg) : e);
                abort();
                break;
              }
            }
          }
        }
      })
      .catch((err) => {
        error && error(err, err.name === 'AbortError');
      });

    return abort;
  }

  async getSettins(): Promise<Settings> {
    const token = this.access.getToken();
    return await getSettins(token).catch(() => ({}));
  }

  async createOrder(): Promise<ResponseWrapper<any>> {
    const token = this.access.getToken();
    const res = await createOrder(token).catch(() => null);
    return res?.data;
  }

  async cancelOrder(id: string): Promise<ResponseWrapper<any>> {
    const token = this.access.getToken();
    const res = await cancelOrder(id, token).catch(() => null);
    return res?.data;
  }

  async getOrder(id: string): Promise<ResponseWrapper<any>> {
    const token = this.access.getToken();
    const res = await getOrder(id, token).catch(() => null);
    return res?.data;
  }

  getImage(path?: string): string | undefined {
    return path ? `${REMOTE}/api/oss/file/${path}` : undefined;
  }

  getOssFile(path?: string): string | undefined {
    return path ? `${REMOTE}/api/oss/file/${path}` : undefined;
  }
}
