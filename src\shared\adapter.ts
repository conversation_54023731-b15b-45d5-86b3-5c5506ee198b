import { request } from '@vtj/utils';

/**
 * 全局请求配置选项接口
 */
export interface GlobalRequestOptions {
  notify?: (msg: string) => void;
  loading?: () => any;
}

/**
 * 设置全局请求配置
 * @param options - 请求配置选项，包含通知和加载状态回调
 * @returns 配置后的request实例
 */
export function setGlobalRequest(options: GlobalRequestOptions) {
  let _loading: any = null;
  const { notify, loading } = options;
  request.setConfig({
    settings: {
      type: 'form', // 默认表单格式提交
      validSuccess: false, // 关闭成功状态提示
      originResponse: false, // 不返回原始响应
      loading: true, // 启用加载状态
      validate: (res: any) => {
        // 响应验证函数 - 支持多种成功状态码和响应格式
        console.log('🔧 API响应验证:', res);

        const code = res.data?.code || res.code;
        const hasSuccessCode = code === 0 || code === 200;
        const hasSuccessFlag = !!res.data?.success || !!res.success;

        // 如果响应中有 id 字段，通常表示操作成功（如 schema 保存）
        const hasIdField = !!res.id || !!res.data?.id;

        const isValid = hasSuccessCode || hasSuccessFlag || hasIdField;
        console.log('🔧 验证结果:', { code, hasSuccessCode, hasSuccessFlag, hasIdField, isValid });

        return isValid;
      },
      failMessage: true, // 启用失败消息提示
      successMessage: false, // 关闭成功消息提示
      showSuccess: () => {
        // 空函数，阻止成功提示
      },
      showError: (msg: string) => {
        // 错误消息显示回调
        if (notify) {
          notify(msg || '未知错误');
        }
      },
      showLoading: () => {
        // 显示加载状态回调
        if (_loading) {
          _loading.close();
        }
        if (loading) {
          _loading = loading();
        }
      },
      hideLoading: () => {
        // 隐藏加载状态回调
        if (_loading) {
          _loading.close();
          _loading = null;
        }
      }
    }
  });
  return request;
}
