import { BlockSchema } from '@vtj/core';

/**
 * 本地缓存管理器 - 解决数据库事务延迟问题
 */
class ServiceCache {
  private fileCache = new Map<string, BlockSchema>();
  private cacheTimeout = 5000; // 5秒缓存过期

  /**
   * 生成缓存键
   */
  private getCacheKey(app: string, fileId: string): string {
    return `${app}:${fileId}`;
  }

  /**
   * 保存文件到缓存
   */
  setFile(app: string, file: BlockSchema): void {
    const key = this.getCacheKey(app, file.id as string);
    this.fileCache.set(key, { ...file });
    
    // 设置过期清理
    setTimeout(() => {
      this.fileCache.delete(key);
    }, this.cacheTimeout);
    
    console.log('📦 ServiceCache.setFile 缓存文件:', key);
  }

  /**
   * 从缓存获取文件
   */
  getFile(app: string, fileId: string): BlockSchema | null {
    const key = this.getCacheKey(app, fileId);
    const cached = this.fileCache.get(key);
    
    if (cached) {
      console.log('🎯 ServiceCache.getFile 命中缓存:', key);
      return { ...cached };
    }
    
    return null;
  }

  /**
   * 清除指定文件缓存
   */
  clearFile(app: string, fileId: string): void {
    const key = this.getCacheKey(app, fileId);
    this.fileCache.delete(key);
    console.log('🗑️ ServiceCache.clearFile 清除缓存:', key);
  }

  /**
   * 清除所有缓存
   */
  clearAll(): void {
    this.fileCache.clear();
    console.log('🗑️ ServiceCache.clearAll 清除所有缓存');
  }
}

export const serviceCache = new ServiceCache();