<template>
  <ElContainer class="layout">
    <ElHeader>
      <h1 class="logo">
        <img :src="logo" />
        低代码开发平台
      </h1>
      <div class="action">
        <ElButton :icon="Plus" type="primary" @click="onCreateApp">
          创建应用
        </ElButton>
        <ElButton :icon="SwitchButton" type="default" @click="onLogout">
          退出
        </ElButton>
      </div>
    </ElHeader>
    <ElContainer>
      <ElMain>
        <ElRow v-if="list.length" :gutter="20">
          <ElCol
            :span="12"
            :xl="6"
            :lg="8"
            :md="12"
            :xs="24"
            v-for="item in list">
            <AppCard :item="item" @refresh="load" @edit="onEdit"></AppCard>
          </ElCol>
        </ElRow>
        <ElEmpty v-else description="您还没创建过任何应用"></ElEmpty>
      </ElMain>
    </ElContainer>
  </ElContainer>
  <AppForm
    v-if="showCreateAppDialog"
    v-model="showCreateAppDialog"
    :data="editItem"
    @submit="onSubmit"></AppForm>
</template>
<script lang="ts" setup>
import { ref, type Ref } from 'vue';
import { useRouter } from 'vue-router';
import {
  ElContainer,
  ElMain,
  ElHeader,
  ElButton,
  ElRow,
  ElCol,
  ElEmpty
} from 'element-plus';
import { Plus, SwitchButton } from '@element-plus/icons-vue';
import AppCard from '@/components/AppCard.vue';
import AppForm from '@/components/AppForm.vue';
import { useApps } from '@/hooks';
import type { LowCodeAppVO } from '@/shared';
import { useAccess } from '@vtj/renderer';
import logo from '@/assets/logo.png';

const access = useAccess();
const router = useRouter();
const { list, load } = useApps();
const showCreateAppDialog = ref(false);
const editItem: Ref<LowCodeAppVO | null> = ref(null);

const onCreateApp = () => {
  editItem.value = null;
  showCreateAppDialog.value = true;
};

const onEdit = (item: LowCodeAppVO) => {
  editItem.value = item;
  showCreateAppDialog.value = true;
};

const onLogout = () => {
  access.logout();
};

const onSubmit = (data: LowCodeAppVO) => {
  if (editItem.value) {
    load(); // 编辑模式：重新加载列表
  } else {
    load(); // 新建模式：也重新加载列表，然后跳转
    const { platform, name } = data;
    // 稍微延迟跳转，确保列表更新完成
    setTimeout(() => {
      router.push(`/${platform.toLowerCase()}/${name}`);
    }, 100);
  }
};
</script>

<style lang="scss" scoped>
.layout {
  height: 100%;
  min-width: 400px;
  padding-top: 60px;
}
.el-header {
  border-bottom: 1px solid var(--el-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 1;
  background-color: var(--el-bg-color);
}
.logo {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  img {
    height: 24px;
    margin-right: 5px;
  }
}
</style>
