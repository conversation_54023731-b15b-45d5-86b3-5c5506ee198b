import { createViteConfig, getConfig } from '@vtj/cli';
import { createDevTools } from '@vtj/pro/vite';
import proxy from './proxy.config';

function createPages(buildType?: string) {
  const pages = {
    main: './index.html',
    web: './web/index.html',
    h5: './h5/index.html',
    uniapp: './uniapp/index.html'
  };
  if (buildType) {
    return {
      [buildType]: pages[buildType]
    };
  }
  return pages;
}

// 获取环境变量
const config = getConfig('./', process.env.ENV_TYPE);
const { BASE_PATH, OUT_DIR } = config;

export default createViteConfig({
  base: BASE_PATH,
  outDir: OUT_DIR,
  proxy,
  elementPlus: true,
  loading: false,
  // 主页面打包时清空dist文件夹
  emptyOutDir: process.env.BUILD_TYPE === 'main',
  plugins: [createDevTools({ link: false })],
  pages: createPages(process.env.BUILD_TYPE),
  alias: {
    // uniapp 打包时用 @dcloudio/uni-h5-vue 替换 vue
    vue: process.env.BUILD_TYPE === 'uniapp' ? '@dcloudio/uni-h5-vue' : 'vue'
  },
  // 定义环境变量，使其在客户端代码中可用
  define: {
    'process.env.REMOTE': JSON.stringify(config.REMOTE || 'https://lcdp.vtj.pro'),
    'process.env.AUTH_CODE': JSON.stringify(config.AUTH_CODE || ''),
    'process.env.BASE_PATH': JSON.stringify(config.BASE_PATH || '/'),
  }
});
